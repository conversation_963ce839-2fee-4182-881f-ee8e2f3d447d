import React, { Component } from 'react';
import {
  View,
  Text,
  Alert,
  FlatList,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { TextInput } from '../../components';
import { colors } from '../../styles';
import { FEATURE_FLAGS, chatClient } from '../../constants';
import nasm from '../../dataManager/apiConfig';
import { logClubConnectChatInitiated } from '../../util/Analytics';
import DashboardProfile from '../../components/DashboardProfile';

class NewConversation extends Component {
  constructor(props) {
    super(props);
    this.state = {
      search: '',
      clients: [],
      groups: [],
      checkingClient: false,
      searching: false,
      loading: true,
      club_id: this.props.trainerActiveProfile?.ClubId,
      location_id: this.props.trainerActiveProfile?.Locations?.Id,
    };
  }

  componentDidMount() {
    this.setNavigationParams();
    const allClients = [];
    const allGroups = [];
    const { club_id, location_id } = this.state;
    nasm.api
      .getTrainerClients(club_id, location_id)
      .then((clients) => {
        allClients.push(clients);
        this.setState({ clients });
        nasm.api
          .getTrainerGroups(club_id, location_id)
          .then((groups) => {
            allGroups.push(groups);
            this.setState({ groups, loading: false });
          })
          .catch((error) => Alert.alert('error', error.message));
      })
      .catch((error) => Alert.alert('error', error.message));
  }

  onPressGroup = async ({ item: group }) => {
    if (this.state.checkingClient) return;
    this.setState({ checkingClient: true });
    try {
      const queryResult = await chatClient.queryUsers({ id: group.id });
      if (!queryResult.users?.length) {
        const { club_id, location_id } = this.state;
        await nasm.api.createChatGroup(group.id, club_id, location_id);
      }
      const groupMembers = group.client_group_clients.map(
        (client) => client.client_user?.user?.id,
      );
      const channelOptions = {
        name: group.title,
        isGroup: true,
        members: [...groupMembers, this.props.currentUser.id],
      };
      if (this.props.trainerActiveProfile?.ClubId) {
        channelOptions.club_id = this.props.trainerActiveProfile?.ClubId;
        if (
          FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED
          && this.props.trainerActiveProfile?.Locations?.Id
        ) {
          channelOptions.location_id = this.props.trainerActiveProfile?.Locations?.Id;
        }
      }
      const channel = chatClient.channel('messaging', group.id, channelOptions);
      await channel.watch();

      // Track ClubConnect chat initiation analytics
      if (this.props.trainerActiveProfile?.ClubId) {
        logClubConnectChatInitiated(
          this.props.currentUser.id,
          this.props.trainerActiveProfile,
          'group',
          groupMembers.length + 1, // +1 for the trainer
          group.id
        );
      }

      this.props.navigation.navigate('Channel', {
        channel,
        title: group.title ?? '',
        isGroup: true,
      });
    } catch (error) {
      Alert.alert('error', error.message);
    } finally {
      this.setState({ checkingClient: false });
    }
  };

  onPressClient = async (client) => {
    if (this.state.checkingClient) return;
    this.setState({ checkingClient: true });
    try {
      const queryResult = await chatClient.queryUsers({ id: client.id });
      if (queryResult.users?.length === 0) {
        await nasm.api.createChatUser(client.id);
      }
      const channelOptions = {
        members: [client.id, this.props.currentUser.id],
      };
      if (this.props.trainerActiveProfile?.ClubId) {
        channelOptions.club_id = this.props.trainerActiveProfile?.ClubId;
        if (
          FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED
          && this.props.trainerActiveProfile?.Locations?.Id
        ) {
          channelOptions.location_id = this.props.trainerActiveProfile?.Locations?.Id;
        }
      }
      const channel = chatClient.channel('messaging', channelOptions);
      await channel.watch();

      // Track ClubConnect chat initiation analytics
      if (this.props.trainerActiveProfile?.ClubId) {
        logClubConnectChatInitiated(
          this.props.currentUser.id,
          this.props.trainerActiveProfile,
          'individual',
          2, // trainer + client
          null
        );
      }

      this.props.navigation.navigate('Channel', {
        channel,
        title: queryResult.users.length ? queryResult.users[0].name : '',
      });
    } catch (error) {
      Alert.alert('error', error.message);
    } finally {
      this.setState({ checkingClient: false });
    }
  };

  setNavigationParams = () => {
    this.props.navigation.setOptions({
      tabBarVisible: false,
      title: 'New Conversation',
    });
  };

  setSearchText = (search) => {
    this.setState({ search, searching: true });
  };

  renderGroup = ({ item, index }) => (
    <DashboardProfile
      groupInfo={item}
      key={index}
      onPress={(active) => this.onPressGroup({ item, active })}
    />
  );

  renderClient = ({ item, index }) => (
    <DashboardProfile
      key={index}
      profileImage={item.avatar_url}
      onPress={() => this.onPressClient(item)}
      name={`${item.full_name}`}
      active={!!item.client_user.active_under_current_trainer}
    />
  );

  renderGroupList = () => (
    <FlatList
      data={this.state.groups.filter((group) => group.title.toLowerCase().includes(this.state.search.toLowerCase()))}
      extraData={this.state.search}
      keyExtractor={(item) => item.id}
      renderItem={this.renderGroup}
    />
  );

  render() {
    const {
      loading, search, clients, groups, searching,
    } = this.state;
    if (loading) {
      return (
        <View style={styles.loaderView}>
          <ActivityIndicator size="large" color={colors.nasmBlue} />
        </View>
      );
    }
    const filteredClients = clients?.filter((client) => client?.full_name?.toLowerCase().includes(search.toLowerCase()));
    const filteredGroups = groups?.filter((group) => group?.title?.toLowerCase().includes(search.toLowerCase()));
    return (
      <View style={styles.container}>
        <View style={styles.inputContainer}>
          <Text style={styles.to}>To:</Text>
          <TextInput
            placeholder="Name"
            showIcon={false}
            value={search}
            style={styles.textInput}
            onChangeText={(searchTxt) => this.setSearchText(searchTxt)}
          />
        </View>
        {searching && !filteredClients.length && !filteredGroups.length ? (
          <View style={styles.emptyResultContainer}>
            <Text style={styles.emptyResultTitle}>No Clients/Groups Found</Text>
            <Text style={styles.emptyResultSubTitle}>
              You don&apos;t have any client or group under that name.
            </Text>
          </View>
        ) : null}
        <FlatList
          style={styles.flatList}
          data={filteredClients}
          extraData={search}
          keyExtractor={(item) => item.id}
          renderItem={this.renderClient}
          ListHeaderComponent={this.renderGroupList}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  inputContainer: {
    paddingTop: 20,
    paddingHorizontal: 20,
  },
  to: {
    fontSize: 14,
    color: colors.subGrey,
    fontFamily: 'Avenir-Medium',
  },
  emptyResultTitle: {
    fontFamily: 'Avenir-Medium',
    fontSize: 15,
    color: colors.black,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 110,
  },
  emptyResultSubTitle: {
    fontFamily: 'Avenir-Book',
    fontSize: 15,
    color: colors.cloudyBlue,
    textAlign: 'center',
    alignSelf: 'center',
    marginTop: 8,
  },
  loaderView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInput: {
    color: colors.black,
    fontFamily: 'Avenir-Medium',
    fontSize: 17,
    flex: 1,
  },
  emptyResultContainer: {
    alignItems: 'center',
  },
  flatList: {
    flex: 1,
  },
});

NewConversation.propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired,
    setOptions: PropTypes.func.isRequired,
  }).isRequired,
  currentUser: PropTypes.shape({
    id: PropTypes.string.isRequired,
  }).isRequired,
  trainerActiveProfile: PropTypes.shape({
    ClubId: PropTypes.string,
    ClubName: PropTypes.string,
    Locations: PropTypes.shape({
      Id: PropTypes.string,
    }),
  }),
};

NewConversation.defaultProps = {
  trainerActiveProfile: null,
};

const mapStateToProps = ({ currentUser, trainerActiveProfile }) => ({
  currentUser,
  trainerActiveProfile,
});

const mapDispatchToProps = {};

// eslint-disable-next-line react-redux/prefer-separate-component-file
export default connect(mapStateToProps, mapDispatchToProps)(NewConversation);
