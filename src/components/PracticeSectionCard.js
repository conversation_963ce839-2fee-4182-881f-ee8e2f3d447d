import React, { Component } from 'react';

// Components
import {
  View, TouchableOpacity, Image, Text,
} from 'react-native';
import IconFeader from 'react-native-vector-icons/Feather';

import ScaledText from './ScaledText';
import { scaleHeight, scaleWidth } from '../util/responsive';

// Styles
import { colors, shadow } from '../styles';

class PracticeSectionCard extends Component {
  constructor(props) {
    super(props);
    this.state = {
      cardTitle: this.props.cardTitle,
    };
  }

  practiceSectionCardFunction = () => {
    if (this.props.visible) {
      return (
        <TouchableOpacity
          onPress={() => this.props.navigation.navigate(this.props.goTo)}
          style={{
            ...styles.cardContainer,
            marginRight: this.props.end ? scaleWidth(9) : scaleWidth(2.5),
          }}
        >
          <Image style={styles.bgImage} source={this.props.image} />
          <View style={styles.content}>
            <Text style={styles.titleText}>{this.props.cardTitle}</Text>
          </View>
          <View
            style={{
              position: 'absolute',
              right: 15,
              top: 50,
            }}
          >
            <IconFeader name="chevron-right" size={25} color={colors.white} />
          </View>
        </TouchableOpacity>
      );
    }
    return null;
  };

  render() {
    return this.practiceSectionCardFunction();
  }
}

const styles = {
  cardContainer: {
    height: 120,
    width: scaleWidth(43.8),
    backgroundColor: colors.subGrey,
    borderRadius: 16,
    ...shadow,
  },
  bgImage: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 16,
  },
  content: {
    margin: '8%',
    flexGrow: 1,
    justifyContent: 'space-evenly',
  },
  titleText: {
    fontWeight: 'bold',
    fontSize: 18,
    color: colors.white,
    marginLeft: 15,
  },
  viewButtonText: {
    textAlign: 'center',
    fontSize: 17,
    color: colors.white,
  },
  viewButton: {
    height: '12%',
    width: '68%',
    borderRadius: scaleWidth(10),
    marginTop: 15,
    justifyContent: 'center',
    borderColor: colors.white,
    borderWidth: 2,
    marginLeft: 10,
  },
};

export default PracticeSectionCard;
