/*
  - This component can be supplied to the headerRight argument of React-Navigation
  - It will inherit some select properties detailed in the propTypes below
  - It was designed because the default React-Navigation header does not handle custom images well
*/
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';

// Components
import { Text, TouchableOpacity } from 'react-native';

// Styles
import { colors } from '../styles';

// PropTypes
const propTypes = {
  // These props can be passed manually or by React-Navigation
  title: PropTypes.string,
  onPress: PropTypes.func.isRequired,
  buttonImage: PropTypes.any,
  titleStyle: PropTypes.any,
  disabled: PropTypes.bool,
  style: PropTypes.object,
};

const defaultProps = {
  title: null,
  buttonImage: null,
  titleStyle: null,
  testID: null,
  disabled: false,
  style: {},
};

// Component Class
class HeaderRightButton extends PureComponent {
  render() {
    const {
      onPress,
      title,
      buttonImage,
      disabled,
      style,
      titleStyle,
    } = this.props;
    return (
      <TouchableOpacity
        style={[styles.container, { style }]}
        onPress={onPress}
        disabled={disabled}
        testID="HeaderRightButton"
      >
        {buttonImage || (
          <Text style={[styles.titleText, titleStyle, styles.noMargin]}>
            {title}
          </Text>
        )}
      </TouchableOpacity>
    );
  }
}

// Export
HeaderRightButton.propTypes = propTypes;
HeaderRightButton.defaultProps = defaultProps;
export default HeaderRightButton;

// Stylesheet
const styles = {
  container: {
    padding: 10,
    alignItems: 'center',
    width: 80,
  },
  titleText: {
    fontFamily: 'Avenir',
    fontSize: 14,
    fontWeight: '500',
    color: colors.white,
    alignSelf: 'flex-end',
  },
  noMargin: {
    margin: 0,
  },
};
