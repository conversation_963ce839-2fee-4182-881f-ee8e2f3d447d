import React, { Component } from 'react';
import PropTypes from 'prop-types';

// Components
import {
  Image,
  Platform,
  Text,
  TextInput,
  StyleSheet,
  View,
} from 'react-native';
import { curvedScale } from '../util/responsive';

// Styles
import { colors } from '../styles';
import { removeAllSpecialCharacters } from '../util/validate';

// Images
const validateYes = require('../resources/validateYes.png');
const validateNo = require('../resources/validateNo.png');

const validateYesWhite = require('../resources/imgValidationCheckmarkWhite.png');
const validateNoWhite = require('../resources/imgValidationDotsWhite.png');

// PropTypes
const propTypes = {
  ...TextInput.propTypes,
  inputText: PropTypes.object,
  validation: PropTypes.func,
  accentValidation: PropTypes.func,
  secureTextEntry: PropTypes.bool,
  testID: PropTypes.string,
  whiteIcons: PropTypes.bool,
  placeholderTextColor: PropTypes.string,
  validationErrorMsg: PropTypes.string,
};
const defaultProps = {
  inputText: {},
  containerStyle: {},
  placeholder: 'Enter text here',
  validation() {
    return false;
  },
  accentValidation() {
    return false;
  },
  secureTextEntry: false,
  testID: null,
  showIcon: true,
  whiteIcons: false,
  placeholderTextColor: null,
  validationErrorMsg: '',
};

// Class
class CustomTextInput extends Component {
  getIcon = () => {
    const { value, validation, accentValidation } = this.props;
    const isValid = validation(value) && !accentValidation(value);
    if (this.props.whiteIcons && isValid) {
      return validateYesWhite;
    }
    if (this.props.whiteIcons) {
      return validateNoWhite;
    }
    if (isValid) {
      return validateYes;
    }
    return validateNo;
  };

  renderValidationError() {
    if (this.props.validationErrorMsg.length) {
      return (
        <Text style={styles.validationErrorStyle}>
          {this.props.validationErrorMsg}
        </Text>
      );
    }
    return null;
  }

  render() {
    const otherProps = { ...this.props };
    delete otherProps.onChangeText;
    return (
      <View>
        <View
          style={[styles.container, this.props.containerStyle]}
          testId={this.props.testID}
        >
          <TextInput
            autoCapitalize="none"
            autoCorrect={this.props.autoCorrect || false}
            placeholder={this.props.placeholder}
            placeholderTextColor={
              this.props.placeholderTextColor
                ? this.props.placeholderTextColor
                : colors.textPlaceholder
            }
            onChange={this.props.onChange}
            style={[styles.inputText, this.props.inputText]}
            onChangeText={(text) => {
              const filteredText = removeAllSpecialCharacters(text);
              this.props.onChangeText(filteredText.trimLeft());
            }}
            ref={(ref) => {
              this.textInput = ref;
            }}
            selectionColor={colors.nasmBlue}
            {...otherProps}
          />
          {this.props.showIcon && <Image source={this.getIcon()} />}
        </View>
        {this.renderValidationError()}
      </View>
    );
  }
}

// Export
CustomTextInput.propTypes = propTypes;
CustomTextInput.defaultProps = defaultProps;
export default CustomTextInput;

// Styles
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'stretch',
    height: curvedScale(46),
    marginTop: 15,
    backgroundColor: colors.transparent,
    borderColor: colors.silver,
    borderBottomWidth: 1,
    marginBottom: 20,
  },
  inputText: {
    flexGrow: 1,
    textAlignVertical: 'bottom',
    paddingHorizontal: 0,
    paddingTop: 0,
    paddingBottom: Platform.OS === 'android' ? 5 : 0,
    fontFamily: 'Avenir-Medium',
    fontSize: curvedScale(14),
    color: colors.black,
  },
  validationErrorStyle: {
    color: colors.rustyRed,
    textAlign: 'left',
    fontSize: 11,
  },
});
