import { createSlice } from '@reduxjs/toolkit';
import { normalize, denormalize, schema } from 'normalizr';
import analytics from '@react-native-firebase/analytics';
import moment from 'moment';
import { v4 as uuidV4 } from 'uuid';
import nasm from '../dataManager/apiConfig';
import { programContexts } from './programContextReducer';
import { possessiveName } from '../util/validate';
import { LOGOUT } from '../actions/currentUserActions';
import { track } from '../util/Analytics';
import { logException } from '../util/logging';
import { FEATURE_FLAGS } from '../constants';
import { formatSuperSets, unformatSuperSets } from '../util/programUtils';

// *****
// Normalizr Schemas
// *****
const workoutSchema = new schema.Entity('workouts', {}, { idAttribute: 'key' });
const programSchema = { workouts: [workoutSchema] };

const initialState = {
  loading: false,
  loadingDetail: false,
  error: false,
  programChanged: false,
  workoutAdded: false,
  // Duplicate might not be needed, there is a dedicated API route for this
  duplicate: false,
  newProgram: false,
  // is_visible: true,
  editable: true,
  correctiveExercises: [],
  program: null,
  entities: null,
  correctiveExercisesEnabled: false,
};

export const selectedProgramSlice = createSlice({
  name: 'selectedProgram',
  initialState,
  reducers: {
    // -----
    // ERROR RECEIVED
    programErrorReceived: (state, action) => {
      const error = action.payload;
      state.loadingDetail = false;
      state.loading = false;
      if (error) {
        state.error = error;
      }
    },
    // -----
    // GET WORKOUT DETAILS
    programDetailsRequested: (state, action) => {
      const { programId, scheduleId, editable } = action.payload;
      return {
        ...initialState,
        loadingDetail: true,
        error: false,
        programId,
        scheduleId,
        editable,
      };
    },
    programDetailsReceived: (state, action) => {
      const response = action.payload;
      state.loadingDetail = false;
      state.programChanged = false;
      state.workoutAdded = false;
      state.loading = false;
      const newWorkouts = formatSuperSets(response.workouts);
      const newProgram = {
        ...response,
        workouts: newWorkouts,
      };
      const keyedProgram = addWorkoutKeys(newProgram);
      const { result, entities } = normalize(keyedProgram, programSchema);
      state.program = result;
      state.entities = {
        workouts: entities.workouts || {},
      };
    },
    // -----
    // SAVE PROGRAM
    saveProgramRequested: (state, action) => {
      state.loading = true;
      state.program.name = action.payload.name;
    },
    saveProgramRecevied: (state, action) => {
      const response = action.payload;
      state.loading = false;
      if (response.error) {
        state.error = response.error;
      } else {
        state.programChanged = false;
        state.workoutAdded = false;
      }
    },
    // -----
    // SCHEDULE PROGRAM
    scheduleProgramRequested: (state, action) => {
      state.loading = true;
      state.program.name = action.payload.name;
    },
    scheduleProgramRecevied: (state, action) => {
      const response = action.payload;
      state.loading = false;
      if (response.error) {
        state.error = response.error;
      } else {
        state.programChanged = false;
        state.workoutAdded = false;
      }
    },
    // -----
    // DUPLICATE PROGRAM
    duplicateProgramRequested: (state) => {
      state.duplicate = true;
      state.loading = true;
    },
    duplicateProgramReceived: (state, action) => {
      const response = action.payload;
      const keyedProgram = addWorkoutKeys(response);
      const { result, entities } = normalize(keyedProgram, programSchema);
      state.duplicate = true;
      state.loading = false;
      state.editable = true;
      state.programChanged = false;
      state.workoutAdded = false;
      state.program = result;
      state.entities = {
        workouts: entities.workouts || {},
      };
    },
    // -----
    // CREATE PROGRAM TEMPLATE
    createProgramTemplateRequested: (state) => {
      state.loading = true;
    },
    createProgramTemplateSuccess: (state) => {
      state.loading = false;
    },
    createProgramTemplateFailed: (state) => {
      state.loading = false;
    },
    // -----
    // ADD WORKOUTS
    workoutsRequested: (state) => {
      state.loading = true;
    },
    addWorkouts: (state, action) => {
      const workouts = action.payload;
      const { correctiveExercises } = state;

      state.loading = false;
      state.programChanged = true;
      state.workoutAdded = true;
      workouts.forEach((workout) => {
        const key = workout.key || uuidV4();
        const correctiveWorkout = state.correctiveExercises.length
          ? addCorrectiveExercisesToWorkout(workout, correctiveExercises)
          : workout;
        state.program.workouts.push(key);
        state.entities.workouts[key] = { key, ...correctiveWorkout };
      });
    },
    // -----
    // LOCAL STATE UPDATES
    assignExercises: (state) => {
      state.is_visible = false;
    },
    editProgramName: (state, action) => {
      state.programChanged = true;
      state.program.name = action.payload;
    },
    editProgramCategory: (state, action) => {
      state.programChanged = true;
      state.program.program_category = action.payload;
    },
    editWorkout: (state, action) => {
      const workout = action.payload;
      state.programChanged = true;
      state.entities.workouts[workout.key] = {
        ...workout,
        exercise_count: countExercises(workout),
      };
    },
    removeWorkout: (state, action) => {
      const workout = action.payload;
      const { workouts } = state.program;
      const index = workouts.findIndex((item) => item === workout.key);
      workouts.splice(index, 1);
      delete state.entities.workouts[workout.key];
      state.programChanged = true;
    },
    sortWorkouts: (state, action) => {
      const newWorkouts = action.payload;
      state.program.workouts = newWorkouts;
      state.programChanged = true;
    },
    clearProgram: () => initialState,
    createNewProgram: (state, action) => {
      const { result, entities } = normalize(action.payload, programSchema);
      state.loading = false;
      state.error = false;
      state.newProgram = true;
      state.programChanged = false;
      state.workoutAdded = false;
      state.program = result;
      state.entities = {
        workouts: entities.workouts || {},
      };
    },
    selectWorkoutDay: (state, action) => {
      const { workoutKey, dayId } = action.payload;
      const { workouts } = state.entities;
      const workout = workouts[workoutKey];

      if (!workout.dayOfWeek) {
        workout.dayOfWeek = [dayId];
      } else {
        const index = workout.dayOfWeek.indexOf(dayId);
        if (index >= 0) {
          // TODO: do we need to sort workout days?
          workout.dayOfWeek.splice(index, 1);
        } else {
          workout.dayOfWeek.push(dayId);
        }
        workout.dayOfWeek = workout.dayOfWeek.sort();
      }
    },
    setWorkoutDays: (state, action) => {
      const { workoutKey, days = [] } = action.payload;
      const { workouts } = state.entities;
      const workout = workouts[workoutKey];

      workout.dayOfWeek = days.sort();
    },
    deleteScheduleRequested: (state) => {
      state.loading = true;
    },
    deleteScheduleSuccess: () => ({
      ...initialState,
      loading: true,
    }),
    selectCorrectiveExercises: (state, action) => {
      state.correctiveExercises = action.payload;
    },
    addCorrectiveExercises: (state) => {
      const { correctiveExercises } = state;
      Object.keys(state.entities.workouts).forEach((key) => {
        const workout = removeCorrectiveExercisesFromWorkout(
          state.entities.workouts[key],
        );
        const newWorkout = addCorrectiveExercisesToWorkout(
          workout,
          correctiveExercises,
        );
        state.entities.workouts[key] = newWorkout;
      });
      state.correctiveExercisesEnabled = true;
    },
    removeCorrectiveExercises: (state) => {
      Object.keys(state.entities.workouts).forEach((key) => {
        const workout = state.entities.workouts[key];
        const newWorkout = removeCorrectiveExercisesFromWorkout(workout);
        state.entities.workouts[key] = newWorkout;
      });
      state.correctiveExercisesEnabled = false;
    },
  },
  extraReducers: {
    [LOGOUT]: () => initialState,
  },
});

// Extract the action creators object and the reducer
export const { actions, reducer } = selectedProgramSlice;
// Extract and export each action creator by name
export const {
  editProgramName,
  editProgramCategory,
  editWorkout,
  removeWorkout,
  sortWorkouts,
  clearProgram,
  selectWorkoutDay,
  setWorkoutDays,
} = actions;

// Export the reducer, either as a default or named export
export default reducer;

// *****
// ACTION CREATORS
// *****

// -----
// SELECT PROGRAM
export const selectProgram = (program) => async (dispatch, getState) => {
  const { id: programId } = program;
  const { currentUser, programContext } = getState();
  const userId = currentUser.id;
  const editable = programContext === programContexts.SCHEDULING
    || programContext === programContexts.RESCHEDULING
    || userId === program.owner_id;

  dispatch(
    actions.programDetailsRequested({ programId, scheduleId: null, editable }),
  );

  const response = await nasm.api
    .getProgramById(programId)
    .catch((error) => ({ error }));

  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }

  dispatch(actions.programDetailsReceived(response));
  return Promise.resolve(response);
};
// -----
// SELECT SCHEDULED PROGRAM
export const selectScheduledProgram = (scheduleDay) => async (dispatch, getState) => {
  const {
    program_id: programId,
    user_schedule_id: scheduleId,
    program_start_date,
    program_end_date,
  } = scheduleDay;
  const clientId = getState().selectedClient?.id;

  dispatch(
    actions.programDetailsRequested({
      programId,
      scheduleId,
      editable: true,
    }),
  );

  const response = await nasm.api
    .getUserAssignedProgramDetails({
      userId: clientId,
      scheduleId,
      program_start_date,
      program_end_date,
    })
    .catch((error) => ({ error }));

  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }

  dispatch(actions.programDetailsReceived(response));
  return Promise.resolve(response);
};
// -----
// CREATE PROGRAM
export const createNewProgram = () => (dispatch) => {
  const blankProgram = {
    name: null,
    id: null,
    key: uuidV4(),
    program_category: null,
    workouts: [],
  };
  dispatch(actions.createNewProgram(blankProgram));
};
// -----
// DUPLICATE PROGRAM
export const duplicateProgram = (programId) => async (dispatch, getState) => {
  const id = programId || getState().selectedProgram.id;
  dispatch(actions.duplicateProgramRequested(id));
  const response = await nasm.api
    .duplicateProgram(id)
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.duplicateProgramReceived(response));
  return Promise.resolve(response);
};
// -----
// SAVE PROGRAM
export const saveProgram = () => async (dispatch, getState) => {
  const { selectedProgram, programContext } = getState();
  const { program, entities, loading } = selectedProgram;
  const { RESCHEDULING } = programContexts;
  if (loading) return null;

  // Call reschedule endpoint if we're editing a scheduled program
  const startDate = moment(program.start_date).isBefore(moment())
    ? moment()
    : moment(program.start_date);
  if (programContext === RESCHEDULING) {
    return dispatch(
      scheduleProgram({
        startDate: startDate.format(),
        endDate: moment(program.end_date).endOf('day').format(),
        oldStartDate: program.start_date,
        oldEndDate: program.end_date,
      }),
    );
  }

  // Program name can be null for quick adds or defaulted for auto save from library
  const name = program.name
    || (programContext === RESCHEDULING
      ? null
      : `New Program ${moment().format('MM-DD')}`);

  // Creating workout_ids in sorted order
  const workout_ids = [];
  program.workouts.map((key) => {
    Object.values(entities.workouts).map((workout) => {
      if (key === workout.key) {
        workout_ids.push(workout.id);
      }
      return null;
    });
    return null;
  });

  // Format request body
  const requestBody = {
    id: program.id || null,
    name,
    workout_ids,
    nasm_program_category_id: program.program_category?.id,
  };

  if (!program.program_category?.id) {
    return null;
  }

  dispatch(actions.saveProgramRequested(requestBody));

  // Save or Create program
  let request;
  if (program.id) {
    request = nasm.api.updateProgram(requestBody);
    analytics().logEvent('edit_custom_program');
  } else {
    request = nasm.api.createProgram(requestBody);
    analytics().logEvent('create_custom_program');
  }

  // Handle response/error
  const response = await request.catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.saveProgramRecevied(response));
  dispatch(actions.programDetailsReceived(response));
  await track('program_created');
  return Promise.resolve(response);
};

// ----
// SAVE PROGRAM AS A TEMPLATE IN LIBRARY
export const createProgramTemplateInLibrary = () => async (dispatch, getState) => {
  const { selectedProgram, selectedClient } = getState();
  const { program, loading } = selectedProgram;
  const {
    id: scheduleId,
    start_date: program_start_date,
    end_date: program_end_date,
  } = program;
  const userId = selectedClient?.id;

  if (loading) return null;

  dispatch(actions.createProgramTemplateRequested());
  // Create Program Template
  const request = nasm.api.createProgramTemplate({
    userId,
    scheduleId,
    program_start_date,
    program_end_date,
  });

  // Handle response/error
  const response = await request.catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.createProgramTemplateFailed());
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.createProgramTemplateSuccess());
  return Promise.resolve(response);
};

// -----
// ADD WORKOUTS
export const addWorkouts = (workouts) => async (dispatch, getState) => {
  const { programContext } = getState();
  const workoutIds = workouts.map((workout) => workout.id);
  dispatch(actions.workoutsRequested(workoutIds));
  const response = await nasm.api
    .getBatchWorkoutsByIds(workoutIds)
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  // if a key was already set for the workouts, add it back is
  workouts.forEach((workout) => {
    if (workout.key) {
      response.find((item) => item.id === workout.id).key = workout.key;
    }
  });
  dispatch(actions.addWorkouts(response));
  if (programContext === programContexts.LIBRARY) {
    await dispatch(saveProgram());
  }
  return Promise.resolve(response);
};
// -----
// QUICK ADD EXERCISES
export const assignExercises = () => (dispatch, getState) => {
  const state = getState();
  const { workouts, selectedClient, selectedGroup } = state;
  const { defaultSections } = workouts;
  const isGroup = selectedGroup?.id;
  let clientOrGroupName = '';
  if (selectedClient?.id) {
    clientOrGroupName = possessiveName(selectedClient?.first_name);
  } else if (isGroup) {
    clientOrGroupName = selectedGroup?.title;
  }
  const blankSections = defaultSections.map((section) => ({
    ...section,
    exercises: [],
  }));
  const newWorkout = {
    name: `${clientOrGroupName} Workout`,
    id: null,
    key: uuidV4(),
    is_visible: false,
    sections: blankSections,
  };
  dispatch(createNewProgram());
  dispatch(actions.addWorkouts([newWorkout]));
  const workout = getState().selectedProgram.entities.workouts[newWorkout.key];
  dispatch(actions.assignExercises(workout));
};
// -----
// SCHEDULE PROGRAM
export const scheduleProgram = ({
  startDate, endDate, oldStartDate, oldEndDate,
}) => async (dispatch, getState) => {
  const {
    selectedProgram,
    selectedClient,
    selectedGroup,
    programContext,
    trainerActiveProfile,
    currentUser,
  } = getState();
  const isGroup = selectedGroup?.id;
  const { program: result, entities } = selectedProgram;
  const program = denormalize(result, programSchema, entities);
  const isNew = programContext === programContexts.SCHEDULING;

  // Only include user_id and alias_name for each exercise object containing an alias
  const workouts = program.workouts.reduce((workoutsAcc, workout) => {
    const sections = workout.sections.reduce((sectionsAcc, section) => {
      const exerciseMappings = section.exercises.reduce(
        (exercisesAcc, sectionExercise) => {
          const { swapped_original_exercise, ...exercise } = sectionExercise;
          if (exercise.alias_name) {
            const aliasMappings = exercise.alias_name.map((alias) => ({
              user_id: alias.user_id,
              alias_name: alias.alias_name,
            }));
            return [
              ...exercisesAcc,
              { ...exercise, alias_name: aliasMappings },
            ];
          }

          return [...exercisesAcc, { ...exercise }];
        },
        [],
      );

      return [...sectionsAcc, { ...section, exercises: exerciseMappings }];
    }, []);

    return [...workoutsAcc, { ...workout, sections }];
  }, []);

  // Name the program for quick add workouts with multiple workouts
  let clientOrGroupName = '';
  if (selectedClient?.id) {
    const clientName = possessiveName(selectedClient?.first_name);
    clientOrGroupName = `${clientName} Program`;
  } else if (isGroup) {
    clientOrGroupName = `${selectedGroup?.title} Program`;
  }
  const defaultProgramName = program.workouts.length > 1 ? clientOrGroupName : null;
  const programName = program.name || defaultProgramName;
  // Update startDate to make sure we do not set a scedule before today
  const newStartDate = moment(startDate).isBefore(moment())
    ? moment().format()
    : startDate;

  const newWorkouts = unformatSuperSets(workouts);

  // Gather data
  const programBody = {
    startDate: newStartDate,
    endDate,
    oldStartDate,
    oldEndDate,
    programName,
    workouts: newWorkouts,
    nasmProgramId: isNew ? program.id : program.nasm_program_id,
  };

  const club_id = trainerActiveProfile?.ClubId;
  const location_id = trainerActiveProfile?.Locations?.Id;

  if (club_id) {
    programBody.club_id = club_id;
    if (FEATURE_FLAGS.CLUB_CONNECT_MULTI_LOCATION_ENABLED && location_id) {
      programBody.location_id = location_id;
    }
  }

  const analyticInfo = {
    start_date: newStartDate,
    end_date: endDate,
    user_id: currentUser?.id,
    full_name: currentUser?.full_name,
  };
  if (selectedClient?.id) {
    analyticInfo.client_id = selectedClient?.id;
  } else if (isGroup) {
    analyticInfo.group_id = selectedGroup?.id;
  }
  if (program.owner_id !== undefined) {
    analyticInfo.custom_program = program.owner_id === null ? 'false' : 'true';
  }
  if (program.id) {
    analyticInfo.program_id = program.id;
  }

  let eventName = '';
  if (club_id) {
    eventName = 'club_connect_';
    analyticInfo.club_name = trainerActiveProfile?.ClubName;
    analyticInfo.club_id = club_id;
  }

  // Make request
  let request;
  if (isNew) {
    if (selectedClient?.id) {
      request = nasm.api.scheduleClientProgram(
        selectedClient?.id,
        programBody,
      );
    } else if (isGroup) {
      request = nasm.api.scheduleForGroup(selectedGroup?.id, programBody);
    }
  } else {
    request = nasm.api.rescheduleClientProgram(
      selectedClient?.id,
      program.id,
      programBody,
    );
  }

  dispatch(
    actions.scheduleProgramRequested({
      ...programBody,
      isNew,
    }),
  );
  const response = await request.catch((error) => ({
    error: {
      name: error.name || 'Error',
      message: error.message || 'Unexpected Error',
    },
  }));
  if (!response || response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.scheduleProgramRecevied(response));
  // Log analytics
  if (isNew) {
    eventName += 'schedule_program';
    await track('workout_assigned', analyticInfo);
  } else {
    eventName += 'reschedule_program';
    await track('workout_reassigned', analyticInfo);
  }
  analytics().logEvent(eventName, analyticInfo);
  // Return
  return Promise.resolve(response);
};
// -----
// QUICK ADD - SCHEDULE PROGRAM TODAY
export const scheduleProgramToday = (day) => (dispatch, getState) => {
  const state = getState();
  const today = moment(day).format();
  const dayId = moment(today).format('d');
  const workoutKeys = state.selectedProgram.program.workouts;
  workoutKeys.forEach((workoutKey) => {
    dispatch(actions.selectWorkoutDay({ workoutKey, dayId }));
  });
  return dispatch(
    scheduleProgram({
      startDate: today,
      endDate: moment(today).endOf('day').format(),
    }),
  );
};
// -----
// REMOVE SCHEDULED PROGRAM
export const deleteScheduledProgram = (programId = null) => async (dispatch, getState) => {
  const { selectedProgram, selectedClient } = getState();
  const scheduleId = programId || selectedProgram.program.id;
  const { start_date: program_start_date, end_date: program_end_date } = selectedProgram.program;
  const userId = selectedClient?.id;
  const currentDate = moment().format();

  dispatch(actions.deleteScheduleRequested());

  const response = await nasm.api
    .deleteClientProgram({
      scheduleId,
      userId,
      currentDate,
      program_start_date,
      program_end_date,
    })
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.deleteScheduleSuccess(response));
  analytics().logEvent('delete_program', {
    client_id: userId,
    program_id: scheduleId,
  });
  return Promise.resolve(response);
};

// -----
// REMOVE SELECTED WORKOUT
export const deleteSingleWorkout = ({ scheduledWorkoutId = null, program_start_date, program_end_date }) => async (dispatch, getState) => {
  const { selectedClient } = getState();
  const userId = selectedClient?.id;
  dispatch(actions.deleteScheduleRequested());
  const response = await nasm.api
    .deleteClientSingleWorkout({
      scheduledWorkoutId,
      userId,
      program_start_date,
      program_end_date,
    })
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.deleteScheduleSuccess(response));
  analytics().logEvent('delete_single_workout', {
    client_id: userId,
    scheduled_workoutId: scheduledWorkoutId,
  });
  return Promise.resolve(response);
};

// -----
// REMOVE ALL INSTANCE OF SELECTED WORKOUT
export const deleteAllInstanceOfWorkout = ({
  userScheduleId = null,
  workoutId = null,
  program_start_date,
  program_end_date,
}) => async (dispatch, getState) => {
  const { selectedClient } = getState();
  const userId = selectedClient?.id;
  dispatch(actions.deleteScheduleRequested());
  const response = await nasm.api
    .deleteClientAllWorkoutInstance({
      userScheduleId,
      workoutId,
      userId,
      program_start_date,
      program_end_date,
    })
    .catch((error) => ({ error }));
  if (response.error) {
    dispatch(actions.programErrorReceived(response.error));
    logException(response.error);
    return Promise.reject(response.error);
  }
  dispatch(actions.deleteScheduleSuccess(response));
  analytics().logEvent('delete_all_workouts', {
    client_id: userId,
    user_scheduleId: userScheduleId,
    workout_id: workoutId,
  });
  return Promise.resolve(response);
};

// ------
// CORRECTIVE EXERCISES
export const selectCorrectiveExercises = (correctiveExercises) => (dispatch) => {
  dispatch(actions.selectCorrectiveExercises(correctiveExercises));
  if (correctiveExercises.length > 0) {
    return dispatch(actions.addCorrectiveExercises());
  }
  return dispatch(actions.removeCorrectiveExercises());
};

export const enableCorrectiveExercises = (enabled = true) => async (dispatch) => {
  if (enabled) {
    return dispatch(actions.addCorrectiveExercises());
  }
  return dispatch(actions.removeCorrectiveExercises());
};

// *****
// HELPER FUNCTIONS
// *****
const countExercises = (workout) => {
  const newExercises = workout.sections.reduce(
    (exercises, section) => exercises.concat(section.exercises),
    [],
  );
  return newExercises.length;
};

const addCorrectiveExercisesToWorkout = (workout, correctiveExercises) => ({
  ...workout,
  exercise_count: workout.exercise_count + correctiveExercises.length,
  sections: workout.sections.map((section) => {
    const newExercises = correctiveExercises.filter(
      (exercise) => exercise.sectionId === section.id,
    );
    if (newExercises.length === 0) return section;
    return {
      ...section,
      exercises: newExercises.concat(section.exercises),
    };
  }),
});

const removeCorrectiveExercisesFromWorkout = (workout) => {
  let exercise_count = 0;
  const sections = workout.sections.map((section) => {
    const exercises = section.exercises.filter((exercise) => !exercise.sectionId);
    exercise_count += exercises.length;
    return {
      ...section,
      exercises,
    };
  });
  return {
    ...workout,
    exercise_count,
    sections,
  };
};

const addWorkoutKeys = (program) => ({
  ...program,
  workouts: (program.workouts || []).map((workout) => ({
    ...workout,
    key: workout.key || uuidV4(),
  })),
});
