apiVersion: batch/v1
kind: Job
metadata:
  name: sequelize-migration
spec:
  template:
    spec:
      containers:
      - name: sequelize-job
        image: nasmdev.azurecr.io/sequelize
        command: ["sequelize",  "db:migrate"]
        imagePullPolicy: IfNotPresent
        envFrom:
          - configMapRef:
              name: nasm-api-config
      restartPolicy: Never
      imagePullSecrets:
      - name: acr-auth
