apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-api
  annotations:
    # use the shared ingress-nginx
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
    - http:
        paths:
          - path: /(k8s/|/|)(.*)
            pathType: Prefix
            backend:
              service:
                name: nasm-api-cluster-ip-service
                port:
                  number: 3000
