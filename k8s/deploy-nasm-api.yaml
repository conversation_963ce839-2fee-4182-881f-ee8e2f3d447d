apiVersion: apps/v1
kind: Deployment
metadata:
  name: nasm-api-deployment
spec:
  replicas: 1
  selector:
    matchLabels:
      component: nasm-api
  template:
    metadata:
      labels:
        component: nasm-api
    spec:
      containers:
        - name: nasm-api
          image: nasmdev.azurecr.io/nasm-api
          imagePullPolicy: Always
          ports:
          - containerPort: 3000
          envFrom:
            - configMapRef:
                name: nasm-api-config
          resources:
            limits:
              cpu: 500m
            requests:
              cpu: 200m
      imagePullSecrets:
      - name: acr-auth
