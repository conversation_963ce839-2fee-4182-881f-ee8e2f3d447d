apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-api-tc-pp
  annotations:
    # use the shared ingress-nginx
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/proxy-body-size: 25M
spec:
  rules:
    - http:
        paths:
          - path: /(k8s/|/|)(.*/admin/privacy-policy)
            pathType: Prefix
            backend:
              service:
                name: nasm-api-cluster-ip-service
                port:
                  number: 3000
          - path: /(k8s/|/|)(.*/admin/terms-conditions)
            pathType: Prefix
            backend:
              service:
                name: nasm-api-cluster-ip-service
                port:
                  number: 3000
